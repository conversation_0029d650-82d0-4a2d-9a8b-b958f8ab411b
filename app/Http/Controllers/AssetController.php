<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;

class AssetController extends Controller
{
    public function assetFile(Request $request, $classLevel, $assetFolder, $assetFile)
    {
        $path = storage_path('app/public/webgl/' . $classLevel . '/' . $assetFolder . '/' . $assetFile);
        if (!File::exists($path)) {
            abort(404);
        }

        return $this->loadFileFromPath($path, $assetFile);
    }

    public function assetFile2(Request $request, $classLevel, $assetFolder, $folderLevel1, $folderLevel2, $assetFile)
    {
        $path = storage_path('app/public/webgl/' . $classLevel . '/' . $assetFolder . '/' . $folderLevel1 . '/' . $folderLevel2 . '/' . $assetFile);
        if (!File::exists($path)) {
            abort(404);
        }

        return $this->loadFileFromPath($path, $assetFile);
    }

    /**
     * @param string $path
     * @param $assetFile
     * @return \Illuminate\Http\Response
     */
    public function loadFileFromPath(string $path, $assetFile): \Illuminate\Http\Response
    {
        ini_set('memory_limit', '256M');
        $file = File::get($path);
        $type = File::mimeType($path);
        if (strpos($assetFile, '.css')) {
            $type = 'text/css';
        }

        if (strpos($assetFile, '.js')) {
            $type = 'text/javascript';
        }

        $response = Response::make($file, 200);
        $response->header("Content-Type", $type);

        return $response;
    }
}
