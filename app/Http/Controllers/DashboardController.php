<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\AccountType;

class DashboardController extends Controller
{
    public function index()
    {
        $account = Account::query()->where('id', auth()->id())->first();
        $tieuhocCourses = collect();
        $mamnonCourses = collect();

        // fetch course tieuhoc
        if($account->is_tieuhoc == 1){
            $tieuhocAccountTypeId = $account->tieuhoc_account_type_id;
            $tieuhocAccountType = AccountType::query()
                ->with('courses')
                ->where('id', $tieuhocAccountTypeId)->first();
            $tieuhocCourses = $tieuhocAccountType->courses;
            // Only filter by class if class contains a digit
            if ($account->class && preg_match('/\d+/', $account->class, $matches)) {
                $grade = $matches[0];
                $tieuhocCourses = $tieuhocCourses->filter(function($course) use ($grade) {
                    return strpos($course->title_en, 'Lớp ' . $grade) !== false;
                });
            }
            // Always sort by course ID
            $tieuhocCourses = $tieuhocCourses->sortBy('id')->values();
        }

        // fetch course mamnon
        if($account->is_mamnon == 1){
            $mamnonAccountTypeId = $account->mamnon_account_type_id;
            $mamnonAccountType = AccountType::query()
                ->with('courses')
                ->where('id', $mamnonAccountTypeId)->first();
            $mamnonCourses = $mamnonAccountType->courses;
            // Only filter by class if class contains a digit
            if ($account->class && preg_match('/\d+/', $account->class, $matches)) {
                $grade = $matches[0];
                $mamnonCourses = $mamnonCourses->filter(function($course) use ($grade) {
                    return strpos($course->title_en, 'Lớp ' . $grade) !== false;
                });
            }
            // Always sort by course ID
            $mamnonCourses = $mamnonCourses->sortBy('id')->values();
        }

        return view('dashboard', compact('account', 'tieuhocCourses', 'mamnonCourses'));
    }
}
