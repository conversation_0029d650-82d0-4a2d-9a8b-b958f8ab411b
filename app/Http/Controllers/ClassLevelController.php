<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;

class ClassLevelController extends Controller
{
    public function index(Request $request, $classLevel)
    {
        $path = storage_path('app/public/webgl/' . $classLevel . '/' . 'index.html');
        if (!File::exists($path)) {
            abort(404);
        }

        $file = File::get($path);
        $type = File::mimeType($path);
        $response = Response::make($file, 200);
        $response->header("Content-Type", 'text/html');

        return $response;
    }
}
