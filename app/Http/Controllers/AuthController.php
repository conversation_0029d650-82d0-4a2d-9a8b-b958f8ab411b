<?php

namespace App\Http\Controllers;

use App\Http\Constants\Constant;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\SignInRequest;
use App\Models\Account;
use Exception;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function register()
    {
        return view('auth.register');
    }

    public function registerStore(RegisterRequest $request)
    {
        try {
            $account = new Account;
            $account->name = $request->name;
            $account->email = $request->email;
            $account->password = Hash::make($request->password);
            $account->status = Constant::STATUS_PENDING;

            if ($account->save()){
                return redirect()->route('login')->with('success', 'Đăng ký thành công vui lòng chờ quản trị viên xác nhận');
            }

            return redirect()->back()->with('danger', 'Đăng ký không thành công, vui lòng thử lại');
        } catch (Exception $e) {
            return redirect()->back()->with('danger', '<PERSON><PERSON> lỗi xảy ra, vui lòng thử lại');
        }
    }

    public function signInForm()
    {
        return view('auth.login');
    }

    public function loginCheck(SignInRequest $request,$back_route)
    {
        try {
            $credentials = $request->only('email', 'password');
            $credentials['status'] = Constant::STATUS_ACTIVE;
            $credentials['deleted_at'] = null;

            if(auth()->attempt($credentials)){
                return redirect()->route($back_route)->with('success', 'Đăng nhập thành công');
            }

            return redirect()->back()->with('error', 'Tài khoản hoặc mật khẩu không đúng!');
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra, vui lòng thử lại sau!');
        }
    }

    public function logout()
    {
        auth()->logout();
        return redirect()->route('login')->with('danger', 'Đăng xuất thành công');
    }
}
