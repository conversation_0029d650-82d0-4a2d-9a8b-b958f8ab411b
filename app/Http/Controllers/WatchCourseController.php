<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\AccountType;
use App\Models\Course;
use App\Models\Lesson;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class WatchCourseController extends Controller
{
    public function watchCourse($type, $id)
    {

        $course = Course::query()->where('id', encryptor('decrypt', $id))->first();
        if($course == null){
            throw new NotFoundHttpException();
        }

        $lessons = Lesson::query()->whereNull('deleted_at')
            ->where('course_id', $course->id)
            ->get();

        $account = Account::query()->where('id', auth()->id())->first();
        if($account == null){
            throw new NotFoundHttpException();
        }

        $materialArr = array();
        if ($type == 1) { // tieu hoc
            $tieuhocAccountTypeId = $account->tieuhoc_account_type_id;
            $tieuhocAccountType = AccountType::query()->where('id', $tieuhocAccountTypeId)->first();
            if($tieuhocAccountType != null){
                $lstString = $tieuhocAccountType->lesson_list;
                $materialArr = explode(',', $lstString);
            }
        }

        if ($type == 0) { // mam non
            $mamnonAccountTypeId = $account->mamnon_account_type_id;
            $mamnonAccountType = AccountType::query()->where('id', $mamnonAccountTypeId)->first();
            if($mamnonAccountType != null){
                $lstString = $mamnonAccountType->lesson_list;
                $materialArr = explode(',', $lstString);
            }
        }

        return view('watchCourse', compact('course', 'lessons', 'account', 'materialArr'));
    }


}
