<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SlashesMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $flag): Response
    {
        if ($flag=="remove") {
            if (str_ends_with($request->getPathInfo(), '/')) {
                $newval = rtrim($request->getPathInfo(), "/");
                $newval = env('APP_URL') . $newval;
                header("Location:$newval");
                exit();
            }
        } else {
            if (!str_ends_with($request->getPathInfo(), '/')) {
                $newval =$request->getPathInfo().'/';
                $newval = env('APP_URL') . $newval;
                header("Location:$newval");
                exit();
            }
        }

        return $next($request);
    }
}
