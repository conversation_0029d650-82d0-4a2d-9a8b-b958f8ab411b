<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'title_en', 'price', 'image'
    ];

    public function lesson()
    {
        return $this->hasMany(Lesson::class);
    }

    public function enrollment()
    {
        return $this->hasMany(Enrollment::class);
    }
}
