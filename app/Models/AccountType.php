<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountType extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'lesson_list', 'code', 'enable_title_prefix', 'title_prefix_format'];

    public function courses() {
        return $this->belongsToMany(Course::class, 'account_type_course','account_type_id','course_id');
    }

    /**
     * Get prefixed title for a material based on its position in the account type's lesson list
     *
     * @param string $originalTitle The original material title
     * @param int $position The position of the material in the lesson list (1-based)
     * @return string The prefixed title or original title if prefix is disabled
     */
    public function getPrefixedTitle($originalTitle, $position)
    {
        if (!$this->enable_title_prefix || empty($this->title_prefix_format)) {
            return $originalTitle;
        }

        $prefix = str_replace('{number}', $position, $this->title_prefix_format);
        return $prefix . $originalTitle;
    }
}
