<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountType extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'lesson_list', 'code', 'enable_title_prefix', 'title_prefix_format'];

    public function courses() {
        return $this->belongsToMany(Course::class, 'account_type_course','account_type_id','course_id');
    }

    /**
     * Generate prefixed title for a material based on its position
     * Strips existing "Bài X - " prefixes to prevent duplicates
     * Replaces numbered patterns like "Bài X", "Chuyên đề X", "Hè X" with just the prefix
     *
     * @param string $originalTitle
     * @param int $position (1-based position in the list)
     * @return string
     */
    public function getPrefixedTitle($originalTitle, $position)
    {
        if (!$this->enable_title_prefix || empty($this->title_prefix_format)) {
            return $originalTitle;
        }

        // Check if title is just a numbered pattern (any word(s) followed by a number)
        // Examples: "Bài 3", "Chuyên đề 1", "Hè 2", "Lesson 5", "Unit 10", etc.
        if (preg_match('/^[^\d\-]+\s*\d+$/u', $originalTitle)) {
            // Replace with just the prefix number (no dash, no original title)
            return str_replace('{number}', $position, $this->title_prefix_format);
        }

        // Strip any existing "Bài X - " prefix first
        $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $originalTitle);

        // Ensure clean title doesn't start with extra spaces
        $cleanTitle = ltrim($cleanTitle);

        // Apply the new prefix
        $prefix = str_replace('{number}', $position, $this->title_prefix_format);

        // Ensure there's a space between prefix and title if needed
        if (!empty($cleanTitle)) {
            $lastChar = substr($prefix, -1);
            if ($lastChar !== ' ') {
                // Add space after any character that isn't already a space
                $prefix .= ' ';
            }
        }

        return $prefix . $cleanTitle;
    }
}
