<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\AccountType;

class PrefixFeatureTest extends TestCase
{
    /**
     * Test that AccountType model has the getPrefixedTitle method
     */
    public function test_account_type_has_get_prefixed_title_method()
    {
        $accountType = new AccountType();
        $this->assertTrue(method_exists($accountType, 'getPrefixedTitle'));
    }

    /**
     * Test getPrefixedTitle method returns original title when prefix is disabled
     */
    public function test_get_prefixed_title_returns_original_when_disabled()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = false;
        $accountType->title_prefix_format = 'Bài {number} - ';
        
        $originalTitle = 'Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);
        
        $this->assertEquals($originalTitle, $result);
    }

    /**
     * Test getPrefixedTitle method returns prefixed title when enabled
     */
    public function test_get_prefixed_title_returns_prefixed_when_enabled()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';
        
        $originalTitle = 'Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);
        
        $this->assertEquals('Bài 1 - Lớp 1 - Chương trình Hè', $result);
    }

    /**
     * Test getPrefixedTitle method with different position numbers
     */
    public function test_get_prefixed_title_with_different_positions()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';
        
        $originalTitle = 'Test Material';
        
        $result1 = $accountType->getPrefixedTitle($originalTitle, 1);
        $this->assertEquals('Bài 1 - Test Material', $result1);
        
        $result5 = $accountType->getPrefixedTitle($originalTitle, 5);
        $this->assertEquals('Bài 5 - Test Material', $result5);
        
        $result10 = $accountType->getPrefixedTitle($originalTitle, 10);
        $this->assertEquals('Bài 10 - Test Material', $result10);
    }

    /**
     * Test getPrefixedTitle method returns original title when format is empty
     */
    public function test_get_prefixed_title_returns_original_when_format_empty()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = '';

        $originalTitle = 'Test Material';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);

        $this->assertEquals($originalTitle, $result);
    }

    /**
     * Test getPrefixedTitle method strips existing "Bài X - " prefixes
     */
    public function test_get_prefixed_title_strips_existing_prefix()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        // Test with existing prefix
        $titleWithPrefix = 'Bài 5 - Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($titleWithPrefix, 1);
        $this->assertEquals('Bài 1 - Lớp 1 - Chương trình Hè', $result);

        // Test with different existing prefix format
        $titleWithSpaces = 'Bài  10  -  Test Material';
        $result2 = $accountType->getPrefixedTitle($titleWithSpaces, 3);
        $this->assertEquals('Bài 3 - Test Material', $result2);
    }

    /**
     * Test getPrefixedTitle method with titles without existing prefix
     */
    public function test_get_prefixed_title_without_existing_prefix()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;
        $accountType->title_prefix_format = 'Bài {number} - ';

        $originalTitle = 'Lớp 1 - Chương trình Hè';
        $result = $accountType->getPrefixedTitle($originalTitle, 1);

        $this->assertEquals('Bài 1 - Lớp 1 - Chương trình Hè', $result);
    }

    /**
     * Test getPrefixedTitle method ensures proper spacing
     */
    public function test_get_prefixed_title_ensures_proper_spacing()
    {
        $accountType = new AccountType();
        $accountType->enable_title_prefix = true;

        // Test with prefix format that doesn't end with space
        $accountType->title_prefix_format = 'Bài {number}';
        $result = $accountType->getPrefixedTitle('Test Material', 1);
        $this->assertEquals('Bài 1 Test Material', $result);

        // Test with prefix format that ends with dash (should add space)
        $accountType->title_prefix_format = 'Bài {number}-';
        $result2 = $accountType->getPrefixedTitle('Test Material', 2);
        $this->assertEquals('Bài 2- Test Material', $result2);

        // Test with prefix format that ends with space
        $accountType->title_prefix_format = 'Bài {number} ';
        $result3 = $accountType->getPrefixedTitle('Test Material', 3);
        $this->assertEquals('Bài 3 Test Material', $result3);
    }
}
