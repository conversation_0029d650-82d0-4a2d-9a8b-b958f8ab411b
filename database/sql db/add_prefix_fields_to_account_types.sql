-- Add prefix fields to account_types table for frontend prefix functionality
-- This script adds the necessary columns to support material title prefixes

-- Add enable_title_prefix column (boolean to enable/disable prefix feature)
ALTER TABLE account_types ADD COLUMN enable_title_prefix BOOLEAN DEFAULT FALSE;

-- Add title_prefix_format column (format string for prefix, e.g., 'Bài {number} - ')
ALTER TABLE account_types ADD COLUMN title_prefix_format VARCHAR(255) DEFAULT 'Bài {number} - ';

-- Optional: Add some sample data for testing
-- UPDATE account_types SET enable_title_prefix = TRUE, title_prefix_format = 'Bài {number} - ' WHERE code = 'tieuhoc';
-- UPDATE account_types SET enable_title_prefix = TRUE, title_prefix_format = 'Bài {number} - ' WHERE code = 'mamnon';
