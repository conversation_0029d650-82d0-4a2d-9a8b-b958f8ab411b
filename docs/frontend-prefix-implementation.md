# Frontend Prefix Feature Implementation

## Overview
This document describes the implementation of the prefix feature for the frontend server. The feature allows materials to be displayed with prefixes like "Bài 1 - " based on account type settings.

## Problem Solved
Previously, materials were displayed with inconsistent prefixes:
- Some had manual "Bài X - " prefixes (e.g., "Bài 5 - Lớp 1 - Ch<PERSON>ơng trình <PERSON>")
- Others had no prefixes (e.g., "Lớp 1 - Chương trình Hè")

With this implementation:
- All materials display with consistent, dynamic prefixes based on their position
- Existing manual prefixes are stripped and replaced with position-based ones
- Example: Both "Bài 5 - Lớp 1 - Chương trình H<PERSON>" and "Lớp 1 - Ch<PERSON>ơng trình <PERSON>" become "Bài 1 - Lớp 1 - <PERSON><PERSON>ơng trình <PERSON>" when at position 1

## Implementation Details

### 1. Database Schema
The following fields are expected to exist in the `account_types` table (added by backend migrations):
- `enable_title_prefix` (BOOLEAN) - Controls whether prefix is enabled for this account type
- `title_prefix_format` (VARCHAR) - Format string for the prefix (e.g., 'Bài {number} - ')

### 2. AccountType Model Enhancement
**File:** `app/Models/AccountType.php`

Added:
- New fillable fields: `enable_title_prefix`, `title_prefix_format`
- Enhanced `getPrefixedTitle($originalTitle, $position)` method that:
  - Returns original title if prefix is disabled
  - **Strips existing "Bài X - " prefixes** using regex to prevent duplicates
  - Replaces `{number}` placeholder with material position
  - Returns clean, consistent prefixed title

### 3. WatchCourseController Updates
**File:** `app/Http/Controllers/WatchCourseController.php`

Changes:
- Added `$userAccountType` variable to store the user's account type object
- Updated both tieuhoc and mamnon sections to use `AccountType::find()` for efficiency
- Set `$userAccountType` for both account types
- Modified return statement to pass `$userAccountType` to the view

### 4. View Template Updates
**File:** `resources/views/watchCourse.blade.php`

Updated two sections where materials are displayed:

#### Section 1: Dropdown Menu (lines 238-260)
- Added PHP logic to calculate material position using `array_search()`
- Applied prefix using `$userAccountType->getPrefixedTitle()`
- Updated `data-material-title` attribute and display text

#### Section 2: Material List (lines 346-383)
- Similar logic for calculating position and applying prefix
- Updated both `data-material-title` and display text
- Maintained existing numbering format while adding prefix

## Key Logic

### Material Position Calculation
```php
$position = array_search($material->content, $materialArr);
if ($position !== false) {
    $displayTitle = $userAccountType->getPrefixedTitle($material->title, $position + 1);
}
```
- Finds the position of the material in the account type's lesson list
- Checks for `false` to handle materials not found in the list
- Adds 1 to convert from 0-based to 1-based indexing

### Prefix Application with Duplicate Prevention
```php
// Strip any existing "Bài X - " prefix first
$cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $originalTitle);

// Apply the new prefix
$prefix = str_replace('{number}', $position, $this->title_prefix_format);
return $prefix . $cleanTitle;
```
- Uses regex `/^Bài\s+\d+\s*-\s*/` to strip existing "Bài X - " prefixes
- Handles various spacing patterns (e.g., "Bài 5 - ", "Bài  10  -  ")
- Applies new position-based prefix to clean title

## Testing
Created comprehensive tests in `tests/Feature/PrefixFeatureTest.php` covering:
- Method existence verification
- Disabled prefix behavior
- Enabled prefix behavior with clean titles
- Different position numbers
- Empty format handling
- **Existing prefix stripping** (new)
- **Mixed scenarios with and without existing prefixes** (new)

All 7 tests pass successfully with 10 assertions.

## Expected Results
When prefix is enabled for an account type:

**Before Enhancement:**
- "Bài 5 - Lớp 1 - Chương trình Hè" → Shows as-is (inconsistent)
- "Lớp 1 - Chương trình Hè" → Shows as-is (no prefix)

**After Enhancement (with prefix enabled, position 1):**
- "Bài 5 - Lớp 1 - Chương trình Hè" → "Bài 1 - Lớp 1 - Chương trình Hè" (consistent)
- "Lớp 1 - Chương trình Hè" → "Bài 1 - Lớp 1 - Chương trình Hè" (prefixed)

**Key Features:**
- Position numbers are calculated based on material order in account type's lesson list
- Existing manual prefixes are automatically stripped and replaced
- Prefix format is configurable per account type
- Feature can be disabled per account type

## Backward Compatibility
- When `enable_title_prefix` is false or null, original titles are displayed
- No breaking changes to existing functionality
- Graceful handling when `$userAccountType` is null
