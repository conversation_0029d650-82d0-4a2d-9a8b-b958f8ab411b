# Frontend Prefix Feature Implementation

## Overview
This document describes the implementation of the prefix feature for the frontend server. The feature allows materials to be displayed with prefixes like "Bài 1 - " based on account type settings.

## Problem Solved
Previously, materials were displayed with raw titles from the database (e.g., "Lớp 1 - <PERSON><PERSON>ơng trình <PERSON>"). With this implementation, they can now display with prefixes (e.g., "Bài 1 - Lớp 1 - Chương trình <PERSON>") when enabled for the account type.

## Implementation Details

### 1. Database Schema
The following fields are expected to exist in the `account_types` table (added by backend migrations):
- `enable_title_prefix` (BOOLEAN) - Controls whether prefix is enabled for this account type
- `title_prefix_format` (VARCHAR) - Format string for the prefix (e.g., '<PERSON>à<PERSON> {number} - ')

### 2. AccountType Model Enhancement
**File:** `app/Models/AccountType.php`

Added:
- New fillable fields: `enable_title_prefix`, `title_prefix_format`
- `getPrefixedTitle($originalTitle, $position)` method that:
  - Returns original title if prefix is disabled
  - Replaces `{number}` placeholder with material position
  - Returns prefixed title when enabled

### 3. WatchCourseController Updates
**File:** `app/Http/Controllers/WatchCourseController.php`

Changes:
- Added `$userAccountType` variable to store the user's account type object
- Updated both tieuhoc and mamnon sections to set `$userAccountType`
- Modified return statement to pass `$userAccountType` to the view

### 4. View Template Updates
**File:** `resources/views/watchCourse.blade.php`

Updated two sections where materials are displayed:

#### Section 1: Dropdown Menu (lines 238-260)
- Added PHP logic to calculate material position using `array_search()`
- Applied prefix using `$userAccountType->getPrefixedTitle()`
- Updated `data-material-title` attribute and display text

#### Section 2: Material List (lines 346-383)
- Similar logic for calculating position and applying prefix
- Updated both `data-material-title` and display text
- Maintained existing numbering format while adding prefix

## Key Logic

### Material Position Calculation
```php
$materialPosition = array_search($material->content, $materialArr) + 1;
```
- Finds the position of the material in the account type's lesson list
- Adds 1 to convert from 0-based to 1-based indexing

### Prefix Application
```php
$displayTitle = $material->title;
if ($userAccountType && $userAccountType->enable_title_prefix) {
    $displayTitle = $userAccountType->getPrefixedTitle($material->title, $materialPosition);
}
```
- Checks if account type exists and has prefix enabled
- Applies prefix using the model method

## Testing
Created comprehensive tests in `tests/Feature/PrefixFeatureTest.php` covering:
- Method existence verification
- Disabled prefix behavior
- Enabled prefix behavior
- Different position numbers
- Empty format handling

All tests pass successfully.

## Expected Results
When prefix is enabled for an account type:
- Materials display as "Bài 1 - Lớp 1 - Chương trình Hè" instead of "Lớp 1 - Chương trình Hè"
- Position numbers are calculated based on material order in account type's lesson list
- Prefix format is configurable per account type
- Feature can be disabled per account type

## Backward Compatibility
- When `enable_title_prefix` is false or null, original titles are displayed
- No breaking changes to existing functionality
- Graceful handling when `$userAccountType` is null
