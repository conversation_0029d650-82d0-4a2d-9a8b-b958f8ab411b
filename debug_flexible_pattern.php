<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\AccountType;

// Test flexible pattern
$accountType = new AccountType();
$accountType->enable_title_prefix = true;
$accountType->title_prefix_format = 'Bài {number} - ';

echo "Testing flexible numbered pattern detection:\n\n";

$testCases = [
    'Bài 3',
    'Chuyên đề 5', 
    'Hè 10',
    'Lesson 7',
    'Unit 15',
    'Chapter 2',
    'Module 8',
    'Bài 3 - Content', // Should NOT match
    'Test 123 Extra', // Should NOT match
];

foreach ($testCases as $title) {
    $result = $accountType->getPrefixedTitle($title, 1);
    $isPattern = preg_match('/^[\p{L}\s]+\d+$/', $title);
    echo "'{$title}' → '{$result}' (Pattern: " . ($isPattern ? 'YES' : 'NO') . ")\n";
}

echo "\nRegex test:\n";
foreach ($testCases as $title) {
    $matches = preg_match('/^[^\d\-]+\s*\d+$/u', $title);
    echo "'{$title}' matches pattern: " . ($matches ? 'YES' : 'NO') . "\n";
}
